<script lang="ts">
  import { Tabs as TabsPrimitive } from 'bits-ui';
  import { cn } from '$lib/utils.js';

  let { ref = $bindable(null), class: className, ...restProps }: TabsPrimitive.ListProps = $props();
</script>

<TabsPrimitive.List
  bind:ref
  data-slot="tabs-list"
  class={cn(
    'dark:border-border bg-muted text-muted-foreground flex h-9 w-full flex-row items-center justify-center gap-2 divide-x p-[3px] px-2 dark:border-b dark:border-t dark:bg-transparent',
    className
  )}
  {...restProps} />
