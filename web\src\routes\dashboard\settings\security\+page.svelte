<script lang="ts">
  // import { page } from '$app/stores';
  import * as Tabs from '$lib/components/ui/tabs';
  import { Lock, Key, Computer } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';

  // Disabled components
  // import TwoFactor from './components/two-factor-disabled.svelte';
  // import Phone from './components/phone-disabled.svelte';
  import Passkeys from './components/passkeys.svelte';
  import PasswordChange from './components/password-change.svelte';
  import Sessions from './components/sessions.svelte';

  export let data;

  console.log('Security page data:', data);
  console.log('Passkeys from data:', data.passkeys);
  console.log('User data from server:', data.userData);

  // Define tabs array for easier management
  const tabs = [
    { id: 'password', label: 'Password', icon: Lock },
    // Two-factor and phone tabs temporarily disabled
    // { id: 'two-factor', label: 'Two-Factor', icon: Shield },
    // { id: 'phone', label: 'Phone', icon: Smartphone },
    { id: 'passkeys', label: 'Passkeys', icon: Key },
    { id: 'sessions', label: 'Sessions', icon: Computer },
  ];

  // Active tab state
  let activeTab = 'password';
</script>

<SEO
  title="Security Settings | Hirli"
  description="Manage your account security settings, including active sessions, two-factor authentication, and connected devices."
  keywords="account security, two-factor authentication, 2FA, sessions, devices, login history"
  url="https://hirli.com/dashboard/settings/security" />

<div class="flex flex-row justify-between p-6">
  <div class="flex flex-col">
    <h2 class="text-lg font-semibold">Security</h2>
    <p class="text-foreground/80">
      Manage your account security settings, including password, two-factor authentication, and
      more.
    </p>
  </div>
</div>

<div class="grid gap-6">
  <div>
    <Tabs.Root value={activeTab} onValueChange={(value) => (activeTab = value)}>
      <Tabs.List class="w-full">
        {#each tabs as tab}
          <Tabs.Trigger value={tab.id}>
            <svelte:component this={tab.icon} class="h-4 w-4" />
            <span>{tab.label}</span>
          </Tabs.Trigger>
        {/each}
      </Tabs.List>

      <!-- Tab Contents -->
      {#each tabs as tab}
        <Tabs.Content value={tab.id}>
          {#if tab.id === 'password'}
            <PasswordChange passwordForm={data.passwordForm} />
          {:else if tab.id === 'passkeys'}
            <Passkeys passkeys={data.passkeys} />
          {:else if tab.id === 'sessions'}
            <Sessions sessions={data.sessions} />
          {/if}
        </Tabs.Content>
      {/each}
    </Tabs.Root>
  </div>
</div>
