<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { toast } from 'svelte-sonner';
  import { Key, Fingerprint, Trash2, Plus, Info } from 'lucide-svelte';
  import { startRegistration } from '@simplewebauthn/browser';
  import { onMount } from 'svelte';

  export let passkeys: any[] = [];

  // Ensure passkeys is always an array
  $: {
    if (!passkeys) {
      console.error('Passkeys is null or undefined');
      passkeys = [];
    } else if (!Array.isArray(passkeys)) {
      console.error('Passkeys is not an array:', passkeys);
      passkeys = [];
    } else {
      console.log('Passkeys component has', passkeys.length, 'passkeys');
      if (passkeys.length > 0) {
        console.log('First passkey:', passkeys[0]);
      }
    }
  }

  onMount(() => {
    console.log('Passkeys component mounted with passkeys:', passkeys);
  });

  // Dialog state
  let showAddDialog = false;
  let passkeyToRemove: any = null;
  let showRemoveDialog = false;

  // Passkey name input
  let passkeyName = '';
  let isRegistering = false;

  // Handle passkey registration
  async function registerPasskey() {
    if (!passkeyName) {
      toast.error('Please provide a name for your passkey');
      return;
    }

    isRegistering = true;

    try {
      console.log('Fetching registration options...');

      // Use the dedicated API endpoint
      const optionsResponse = await fetch('/api/passkeys?action=getRegistrationOptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: passkeyName }),
      });

      if (!optionsResponse.ok) {
        const errorText = await optionsResponse.text();
        console.error('Registration options error:', errorText);
        throw new Error(
          `Failed to get registration options: ${optionsResponse.status} ${errorText}`
        );
      }

      const options = await optionsResponse.json();
      console.log('Registration options received:', options);

      // 2. Start the registration process in the browser
      console.log('Starting registration in browser...');

      try {
        // Following the passkeys.com guide
        console.log('Starting registration with options:', options);
        const registrationResponse = await startRegistration(options);
        console.log('Registration response:', JSON.stringify(registrationResponse, null, 2));

        // Log the challenge from the response
        console.log('Client challenge:', registrationResponse.response.clientDataJSON);

        try {
          // Decode the clientDataJSON to see the challenge
          const clientDataJSON = JSON.parse(atob(registrationResponse.response.clientDataJSON));
          console.log('Decoded clientDataJSON:', clientDataJSON);
          console.log('Challenge from client:', clientDataJSON.challenge);
        } catch (e) {
          console.error('Error decoding clientDataJSON:', e);
        }

        // 3. Send the response to the server for verification
        console.log('Sending verification request...');

        // Use the dedicated API endpoint
        const verificationResponse = await fetch('/api/passkeys?action=verifyRegistration', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: passkeyName,
            registrationResponse,
          }),
        });

        if (!verificationResponse.ok) {
          let errorText = '';
          try {
            const errorData = await verificationResponse.json();
            console.error('Verification error:', errorData);
            errorText = JSON.stringify(errorData);
          } catch (e) {
            errorText = await verificationResponse.text();
            console.error('Verification error (text):', errorText);
          }

          throw new Error(
            `Failed to verify registration: ${verificationResponse.status} ${errorText}`
          );
        }

        const result = await verificationResponse.json();
        console.log('Verification result:', result);

        if (result.success) {
          toast.success('Passkey added successfully');
          passkeys = result.passkeys;
          showAddDialog = false;
          passkeyName = '';
        } else {
          toast.error(result.error || 'Failed to add passkey');
        }
      } catch (err) {
        // Handle specific WebAuthn errors according to the guide
        if (err.name === 'NotAllowedError') {
          // Don't show toast for canceled operations
          console.log('Passkey operation was canceled or timed out');
        } else if (err.name === 'AbortError') {
          // Don't show toast for aborted operations
          console.log('Passkey operation was aborted');
        } else if (err.name === 'InvalidStateError') {
          toast.error('The passkey already exists on this device');
        } else if (err.name === 'SecurityError') {
          toast.error('Security error during passkey operation');
        } else if (err.name === 'ConstraintError') {
          toast.error('Your device may not support passkeys or has reached its limit');
        } else if (err.name === 'NotSupportedError') {
          toast.error('Your browser or device does not support passkeys');
        } else {
          console.error('WebAuthn error:', err);
          toast.error(err.message || 'Failed to create passkey');
        }
      }
    } catch (error) {
      console.error('Passkey registration error:', error);
      toast.error(error.message || 'Failed to register passkey');
    } finally {
      isRegistering = false;
    }
  }

  // Handle passkey removal
  async function removePasskey(passkeyId: string) {
    try {
      console.log('Removing passkey with ID:', passkeyId);

      // Use the API endpoint
      const response = await fetch('/api/passkeys?action=removePasskey', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ passkeyId }),
      });

      if (!response.ok) {
        let errorText = '';
        try {
          const errorData = await response.json();
          console.error('Remove passkey error:', errorData);
          errorText = JSON.stringify(errorData);
        } catch (e) {
          errorText = await response.text();
          console.error('Remove passkey error (text):', errorText);
        }

        throw new Error(`Failed to remove passkey: ${response.status} ${errorText}`);
      }

      const result = await response.json();

      if (result.success) {
        toast.success('Passkey removed successfully');
        passkeys = result.passkeys;
        showRemoveDialog = false;
      } else {
        toast.error(result.error || 'Failed to remove passkey');
      }
    } catch (error) {
      console.error('Error removing passkey:', error);
      toast.error(error.message || 'Failed to remove passkey');
    }
  }

  // Format date
  function formatDate(dateString: string) {
    if (!dateString) {
      return 'Unknown date';
    }
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid date';
    }
  }
</script>

<!-- Passkeys -->

<div class="flex items-center justify-between p-6">
  <div class="flex flex-col">
    <h3 class="text-lg font-semibold">Passkeys</h3>
    <p class="text-muted-foreground">Manage passkeys for passwordless sign-in.</p>
  </div>
  <button
    type="button"
    class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
    on:click={() => (showAddDialog = true)}>
    <Plus class="h-4 w-4" />
    Add Passkey
  </button>
</div>

<div class="space-y-6 p-6 pt-0">
  {#if passkeys.length === 0}
    <div class="rounded-lg border border-dashed p-8 text-center">
      <Fingerprint class="text-muted-foreground mx-auto mb-4 h-10 w-10" />
      <h3 class="mb-2 text-lg font-medium">No passkeys added yet</h3>
      <p class="text-muted-foreground mb-4 text-sm">
        Add a passkey to sign in without a password using your device's biometric authentication or
        PIN.
      </p>

      <div class="mx-auto mb-6 max-w-md">
        <div class="text-muted-foreground mb-2 text-sm font-medium">Benefits of passkeys:</div>
        <ul class="text-muted-foreground list-disc space-y-1 pl-6 text-left text-sm">
          <li>No passwords to remember or type</li>
          <li>More secure than passwords</li>
          <li>Can't be phished or stolen in data breaches</li>
          <li>Works across your devices</li>
        </ul>
      </div>

      <button
        type="button"
        class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground mx-auto inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
        on:click={() => (showAddDialog = true)}>
        <Plus class="h-4 w-4" />
        Add Your First Passkey
      </button>
    </div>
  {:else}
    <div class="space-y-4">
      {#each passkeys as passkey}
        <Card.Root>
          <Card.Content class="flex items-center justify-between rounded-lg">
            <div class="flex w-full items-center gap-4">
              <Key class="text-primary h-8 w-8" />
              <div>
                <p class="font-medium">{passkey.name || 'Unnamed Passkey'}</p>
                <p class="text-muted-foreground text-xs">
                  Created: {formatDate(passkey.createdAt)}
                </p>
                <p class="text-muted-foreground text-xs">
                  Last used: {formatDate(passkey.lastUsed)}
                </p>
                {#if passkey.id}
                  <p class="text-muted-foreground text-xs">
                    ID: {typeof passkey.id === 'string' && passkey.id.length > 10
                      ? passkey.id.substring(0, 10) + '...'
                      : passkey.id}
                  </p>
                {/if}
              </div>
            </div>
            <button
              type="button"
              class="text-destructive hover:text-destructive/90 hover:bg-destructive/10 inline-flex h-8 w-8 items-center justify-center rounded-md p-0"
              on:click={() => {
                console.log('Setting passkey to remove:', passkey);
                passkeyToRemove = passkey;
                showRemoveDialog = true;
              }}>
              <Trash2 class="h-4 w-4" />
            </button>
          </Card.Content>
        </Card.Root>
      {/each}
    </div>
  {/if}

  <div class="bg-muted/40 mt-12 rounded-lg border p-4">
    <div class="flex items-start gap-4">
      <Info class="text-primary mt-1 h-6 w-6" />
      <div>
        <h3 class="font-medium">About Passkeys</h3>
        <p class="text-muted-foreground text-sm">
          Passkeys are a more secure alternative to passwords. They use biometric authentication
          (like fingerprint or face recognition) or a device PIN to sign you in without having to
          remember or type a password.
        </p>
        <p class="text-muted-foreground mt-2 text-sm">
          Your passkey is stored securely on your device and can't be phished or stolen in a data
          breach.
        </p>
        <p class="text-muted-foreground mt-2 text-sm">
          <strong>Tip:</strong> Add passkeys to multiple devices to ensure you can always sign in, even
          if one device is lost or unavailable.
        </p>
        <p class="text-muted-foreground mt-2 text-sm">
          <a
            href="https://passkeys.com/what-are-passkeys/"
            target="_blank"
            rel="noopener noreferrer"
            class="text-primary hover:underline">
            Learn more about passkeys
          </a>
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Add Passkey Dialog -->
<Dialog.Root bind:open={showAddDialog}>
  <Dialog.Overlay />
  <Dialog.Portal>
    <Dialog.Content class="sm:max-w-[425px]">
      <Dialog.Header>
        <Dialog.Title>Add Passkey</Dialog.Title>
        <Dialog.Description>Create a new passkey for passwordless sign-in.</Dialog.Description>
      </Dialog.Header>
      <div class="grid gap-4 py-4">
        <div class="space-y-2">
          <label for="passkey-name" class="text-sm font-medium leading-none">Passkey Name</label>
          <input
            id="passkey-name"
            type="text"
            placeholder="e.g., Work Laptop, Personal Phone"
            bind:value={passkeyName}
            class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm" />
          <p class="text-muted-foreground text-sm">
            Give your passkey a name to help you identify it later
          </p>
        </div>

        <div class="bg-muted rounded-md p-3 text-sm">
          <p class="font-medium">What happens next?</p>
          <ul class="text-muted-foreground mt-2 list-disc pl-5">
            <li>Your device will prompt you to create a passkey</li>
            <li>You may need to use your fingerprint, face, or device PIN</li>
            <li>This passkey will be stored securely on your current device</li>
          </ul>
        </div>
        <Dialog.Footer>
          <button
            type="button"
            class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
            on:click={() => {
              if (!isRegistering) {
                showAddDialog = false;
              } else {
                toast.error('Please wait for passkey registration to complete');
              }
            }}
            disabled={isRegistering}>
            Cancel
          </button>
          <button
            type="button"
            on:click={registerPasskey}
            class="bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            disabled={isRegistering}>
            {isRegistering ? 'Creating...' : 'Create Passkey'}
          </button>
        </Dialog.Footer>
      </div>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<!-- Remove Passkey Dialog -->
<Dialog.Root bind:open={showRemoveDialog}>
  <Dialog.Overlay />
  <Dialog.Portal>
    <Dialog.Content class="sm:max-w-[425px]">
      <Dialog.Header>
        <Dialog.Title>Remove Passkey</Dialog.Title>
        <Dialog.Description>
          Are you sure you want to remove this passkey? This action cannot be undone.
        </Dialog.Description>
      </Dialog.Header>
      <div class="py-4">
        {#if passkeyToRemove}
          <div class="flex items-center gap-4 rounded-lg border p-4">
            <Key class="text-primary h-6 w-6" />
            <div>
              <p class="font-medium">{passkeyToRemove.name || 'Unnamed Passkey'}</p>
              <p class="text-muted-foreground text-xs">
                Created: {formatDate(passkeyToRemove.createdAt)}
              </p>
              {#if passkeyToRemove.id}
                <p class="text-muted-foreground text-xs">
                  ID: {typeof passkeyToRemove.id === 'string' && passkeyToRemove.id.length > 10
                    ? passkeyToRemove.id.substring(0, 10) + '...'
                    : passkeyToRemove.id}
                </p>
              {/if}
            </div>
          </div>
        {/if}
      </div>
      <Dialog.Footer>
        <button
          type="button"
          class="focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50"
          on:click={() => (showRemoveDialog = false)}>
          Cancel
        </button>
        <button
          type="button"
          class="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
          on:click={() => {
            if (passkeyToRemove) {
              // Use credentialID if available, otherwise use id
              const passkeyId = passkeyToRemove.credentialID || passkeyToRemove.id;
              console.log('Removing passkey with ID from dialog:', passkeyId);
              removePasskey(passkeyId);
            }
          }}>
          Remove Passkey
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
