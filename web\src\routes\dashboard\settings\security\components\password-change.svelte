<script lang="ts">
  import { superForm } from 'sveltekit-superforms';
  import { zodClient } from 'sveltekit-superforms/adapters';
  import { z } from 'zod';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { toast } from 'svelte-sonner';
  import { Lock } from 'lucide-svelte';

  // Define the schema with Zod for validation
  const passwordSchema = z
    .object({
      currentPassword: z.string().min(1, 'Current password is required'),
      newPassword: z.string().min(8, 'Password must be at least 8 characters'),
      confirmPassword: z.string().min(1, 'Please confirm your password'),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    });

  export let passwordForm;

  const form = superForm(passwordForm, {
    validators: zodClient(passwordSchema),
    dataType: 'json',
    onUpdated: ({ form, result }) => {
      if (form.valid && result.type === 'success') {
        toast.success('Password updated successfully');
        resetForm();
      }
    },
    onError: () => {
      toast.error('Failed to update password');
    },
  });

  const { form: formData, enhance, errors, submitting } = form;

  // Reset form
  function resetForm() {
    formData.update((f) => ({
      ...f,
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    }));
  }
</script>

<!-- Password Change Form -->
<form method="POST" action="?/changePassword" use:enhance>
  <div class="flex items-center justify-between p-6">
    <div class="flex flex-col">
      <h3 class="text-lg font-semibold">Change Password</h3>
      <p class="text-muted-foreground">Update your account password.</p>
    </div>
  </div>
  <div class="flex flex-col gap-6 p-6 pt-0">
    <div class="space-y-4">
      <div class="space-y-2">
        <label for="currentPassword" class="text-sm font-medium leading-none">
          Current Password
        </label>
        <Input
          id="currentPassword"
          type="password"
          placeholder="Enter your current password"
          bind:value={$formData.currentPassword} />
        {#if $errors.currentPassword}
          <p class="text-destructive text-sm">{$errors.currentPassword}</p>
        {/if}
      </div>

      <div class="space-y-2">
        <label for="newPassword" class="text-sm font-medium leading-none"> New Password </label>
        <Input
          id="newPassword"
          type="password"
          placeholder="Enter your new password"
          bind:value={$formData.newPassword} />
        {#if $errors.newPassword}
          <p class="text-destructive text-sm">{$errors.newPassword}</p>
        {/if}
      </div>

      <div class="space-y-2">
        <label for="confirmPassword" class="text-sm font-medium leading-none">
          Confirm New Password
        </label>
        <Input
          id="confirmPassword"
          type="password"
          placeholder="Confirm your new password"
          bind:value={$formData.confirmPassword} />
        {#if $errors.confirmPassword}
          <p class="text-destructive text-sm">{$errors.confirmPassword}</p>
        {/if}
      </div>
    </div>

    <div class="bg-muted/40 rounded-lg border p-4">
      <div class="flex items-start gap-4">
        <Lock class="text-primary mt-1 h-6 w-6" />
        <div>
          <h3 class="font-medium">Password Security Tips</h3>
          <ul class="text-muted-foreground mt-2 list-disc space-y-1 pl-5 text-sm">
            <li>Use at least 8 characters</li>
            <li>Include uppercase and lowercase letters</li>
            <li>Add numbers and special characters</li>
            <li>Avoid using personal information</li>
            <li>Don't reuse passwords across different sites</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="flex justify-end p-6">
    <Button type="submit" disabled={$submitting}>
      {$submitting ? 'Updating...' : 'Update Password'}
    </Button>
  </div>
</form>
