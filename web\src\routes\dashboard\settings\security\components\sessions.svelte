<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Dialog from '$lib/components/ui/dialog/index.js';
  import { LogOut, Globe, Clock, MapPin, Monitor, Smartphone, Laptop } from 'lucide-svelte';
  import { toast } from 'svelte-sonner';

  export let sessions: any[] = [];

  // Dialog state
  let showLogoutAllDialog = false;

  // Handle session logout
  async function logoutSession(sessionId: string) {
    try {
      // Check if trying to log out current session
      const currentSession = sessions.find((s) => s.isCurrent);
      if (currentSession && currentSession.id === sessionId) {
        toast.error('Cannot log out of current session using this method');
        return;
      }

      // Create FormData and append the sessionId
      const formData = new FormData();
      formData.append('sessionId', sessionId);

      const response = await fetch('?/logoutSession', {
        method: 'POST',
        body: formData,
        headers: {
          Accept: 'application/json',
        },
      });

      const result = await response.json();

      if (result.type === 'success') {
        toast.success('Session logged out successfully');
        // Remove the session from the list
        sessions = sessions.filter((s) => s.id !== sessionId);
      } else {
        toast.error(result.error || 'Failed to log out session');
      }
    } catch (error) {
      console.error('Error logging out session:', error);
      toast.error('Failed to log out session');
    }
  }

  // Handle logout all sessions
  async function logoutAllSessions() {
    try {
      // Create a FormData object to ensure proper content type
      const formData = new FormData();

      const response = await fetch('?/logoutAllSessions', {
        method: 'POST',
        body: formData,
        headers: {
          Accept: 'application/json',
        },
      });

      const result = await response.json();

      if (result.type === 'success') {
        // Show count of revoked sessions if available
        const message =
          result.count !== undefined
            ? `Logged out of ${result.count} other session${result.count !== 1 ? 's' : ''} successfully`
            : 'All other sessions logged out successfully';

        toast.success(message);

        // Keep only the current session
        sessions = sessions.filter((s) => s.isCurrent);
        showLogoutAllDialog = false;
      } else {
        toast.error(result.error || 'Failed to log out all sessions');
      }
    } catch (error) {
      console.error('Error logging out all sessions:', error);
      toast.error('Failed to log out all sessions');
    }
  }

  // Format date relative to now
  function formatRelativeTime(dateString: string) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.round(diffMs / 1000);
    const diffMin = Math.round(diffSec / 60);
    const diffHour = Math.round(diffMin / 60);
    const diffDay = Math.round(diffHour / 24);

    if (diffSec < 60) {
      return 'just now';
    } else if (diffMin < 60) {
      return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
    } else if (diffHour < 24) {
      return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
    } else if (diffDay < 30) {
      return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  // Get device icon
  function getDeviceIcon(device: string) {
    if (
      device.toLowerCase().includes('iphone') ||
      device.toLowerCase().includes('android') ||
      device.toLowerCase().includes('mobile')
    ) {
      return Smartphone;
    } else if (device.toLowerCase().includes('ipad') || device.toLowerCase().includes('tablet')) {
      return Monitor;
    } else {
      return Laptop;
    }
  }
</script>

<!-- Active Sessions -->

<div class="p-6">
  <div class="flex items-center justify-between">
    <div class="flex flex-col">
      <h3 class="text-lg font-semibold">Active Sessions</h3>
      <p class="text-muted-foreground">Manage your active sessions across devices.</p>
    </div>
    {#if sessions.filter((s) => !s.isCurrent).length > 0}
      <button
        class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
        on:click={() => (showLogoutAllDialog = true)}>
        <LogOut class="h-4 w-4" />
        Log Out All Other Sessions
      </button>
    {/if}
  </div>
</div>
<div class="p-6 pt-0">
  <div class="space-y-4">
    {#each sessions as session}
      <Card.Root>
        <Card.Content class="border-border flex items-center justify-between rounded-lg">
          <div class="flex items-start gap-4">
            <svelte:component
              this={getDeviceIcon(session.device)}
              class="text-primary mt-1 h-8 w-8" />
            <div class="space-y-1">
              <div class="flex items-center">
                <p class="font-medium">
                  {session.device}
                </p>
                {#if session.isCurrent}
                  <span
                    class="bg-success/20 text-success ml-2 rounded-full px-2 py-0.5 text-xs font-medium"
                    >Current</span>
                {/if}
              </div>
              <div class="text-muted-foreground flex items-center gap-4 text-sm">
                <div class="flex items-center gap-1">
                  <Globe class="h-3.5 w-3.5" />
                  <span>{session.browser} on {session.os}</span>
                </div>
                <div class="flex items-center gap-1">
                  <MapPin class="h-3.5 w-3.5" />
                  <span>{session.location}</span>
                </div>
              </div>
              <div class="text-muted-foreground flex items-center gap-1 text-xs">
                <Clock class="h-3 w-3" />
                <span>Last active: {formatRelativeTime(session.lastActive)}</span>
              </div>
              {#if session.ip}
                <div class="text-muted-foreground text-xs">
                  IP: {session.ip}
                </div>
              {/if}
            </div>
          </div>
          {#if !session.isCurrent}
            <button
              class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-2 rounded-md border px-3 text-xs font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
              on:click={() => logoutSession(session.id)}>
              <LogOut class="h-4 w-4" />
              Log Out
            </button>
          {/if}
        </Card.Content>
      </Card.Root>
    {/each}
  </div>
</div>

<!-- Logout All Sessions Dialog -->
<Dialog.Root bind:open={showLogoutAllDialog}>
  <Dialog.Overlay />
  <Dialog.Portal>
    <Dialog.Content class="sm:max-w-[425px]">
      <Dialog.Header>
        <Dialog.Title>Log Out All Other Sessions</Dialog.Title>
        <Dialog.Description>
          Are you sure you want to log out all other sessions? You will remain logged in on this
          device.
        </Dialog.Description>
      </Dialog.Header>
      <div class="py-4">
        <p class="text-muted-foreground text-sm">
          This will log you out from {sessions.filter((s) => !s.isCurrent).length} other device(s).
        </p>
      </div>
      <Dialog.Footer>
        <button
          type="button"
          class="ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
          on:click={() => (showLogoutAllDialog = false)}>
          Cancel
        </button>
        <button
          type="button"
          class="bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
          on:click={logoutAllSessions}>
          Log Out All Other Sessions
        </button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>
